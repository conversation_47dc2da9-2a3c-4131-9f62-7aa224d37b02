import 'dart:async';
import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/category_controller.dart';
import 'package:bibl/controllers/heart_controller.dart';
import 'package:bibl/routes/app_routes.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SplashController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Minimum splash display time to ensure smooth experience
  final Duration _minimumSplashTime = const Duration(milliseconds: 1500);

  // Track if initialization is complete
  final RxBool _isInitialized = false.obs;

  @override
  void onInit() {
    super.onInit();
    // Start initialization process
    _initialize();
  }

  // Initialize app and handle navigation
  Future<void> _initialize() async {
    try {
      // Record start time to ensure minimum splash display
      final startTime = DateTime.now();

      // Start initialization in background
      await _initializeApp();

      // Mark initialization as complete
      _isInitialized.value = true;

      // Calculate remaining time to ensure minimum splash display
      final elapsedTime = DateTime.now().difference(startTime);
      final remainingTime = _minimumSplashTime - elapsedTime;

      // Wait for minimum splash time if needed
      if (remainingTime.isNegative) {
        _navigateToNextScreen();
      } else {
        await Future.delayed(remainingTime);
        _navigateToNextScreen();
      }
    } catch (e) {
      debugPrint('Error during initialization: $e');
      // Navigate to auth screen as fallback
      await Future.delayed(_minimumSplashTime);
      AppRoutes.navigateOffAll(AppRoutes.auth);
    }
  }

  // Initialize app dependencies and preload critical data
  Future<void> _initializeApp() async {
    try {
      // Preload critical data that's needed for homepage
      final heartController = Get.find<HeartController>();

      // Wait for critical user data to load
      await heartController.fetchHeartsValue();

      // Preload categories
      await Get.find<CategoryController>().fetchAllCategories();

      debugPrint('Critical app data preloaded successfully');
    } catch (e) {
      debugPrint('Error preloading app data: $e');
      // Continue with navigation even if preloading fails
    }
  }

  // Navigate to the appropriate screen based on auth state
  void _navigateToNextScreen() async {
    try {
      if (_auth.currentUser == null) {
        // User is not logged in, navigate to auth screen
        AppRoutes.navigateOffAll(AppRoutes.auth);
        // Enable auth redirect for subsequent logins
        Get.find<AuthController>().enableAuthRedirect();
      } else {
        // User is logged in, check if they've seen onboarding
        final userDoc = await _firestore
            .collection('users')
            .doc(_auth.currentUser!.uid)
            .get();
        final bool hasSeenOnboarding =
            userDoc.data()?['hasSeenOnboadingScreen'] ?? false;

        if (hasSeenOnboarding) {
          // User has seen onboarding, navigate to home
          AppRoutes.navigateOffAll(AppRoutes.home);
        } else {
          // User hasn't seen onboarding, navigate to onboarding
          AppRoutes.navigateOffAll(AppRoutes.onboarding1);
        }
        // Enable auth redirect for subsequent auth changes
        Get.find<AuthController>().enableAuthRedirect();
      }
    } catch (e) {
      debugPrint('Error navigating from splash: $e');
      // Navigate to auth screen as fallback
      AppRoutes.navigateOffAll(AppRoutes.auth);
      // Enable auth redirect even on error
      try {
        Get.find<AuthController>().enableAuthRedirect();
      } catch (authError) {
        debugPrint('Error enabling auth redirect: $authError');
      }
    }
  }
}
