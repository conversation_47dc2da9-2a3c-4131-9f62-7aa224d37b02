import 'package:get/get.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/category_controller.dart';
import 'package:bibl/controllers/heart_controller.dart';
import 'package:bibl/controllers/lesson_controller.dart';

class AppDataController extends GetxController {
  final RxBool isInitialDataLoaded = false.obs;
  final RxBool isCriticalDataReady = false.obs;

  // Cache critical data that causes UI jumps
  final RxInt cachedHearts = 0.obs;
  final RxInt cachedNeurons = 0.obs;
  final RxInt cachedStreak = 0.obs;
  final RxBool cachedIsPremium = false.obs;
  final RxList<dynamic> cachedCategories = [].obs;

  @override
  void onInit() {
    super.onInit();
    _preloadCriticalData();
  }

  Future<void> _preloadCriticalData() async {
    try {
      final profileController = Get.find<ProfileController>();
      final categoryController = Get.find<CategoryController>();
      final heartController = Get.find<HeartController>();
      final lessonController = Get.find<LessonController>();

      // Load all critical data in parallel
      await Future.wait([
        profileController.getUserData(),
        categoryController.fetchAllCategories(),
        heartController.fetchHeartsValue(),
      ]);

      // Cache the values
      cachedHearts.value = profileController.userr.value.hearts ?? 0;
      cachedNeurons.value = profileController.userr.value.neurons ?? 0;
      cachedStreak.value = profileController.userr.value.weeklyStreak ?? 0;
      cachedIsPremium.value =
          profileController.userr.value.isPremiumUser ?? false;
      cachedCategories.value = categoryController.allCategories;

      // Initialize display items
      if (lessonController.homeDisplayedItems.isEmpty) {
       await lessonController.mergeAndShuffleItems(
          isShuffle: true,
          from: 'app_data_controller',
          shouldClear: false,
        );
      }

      if (lessonController.libraryDisplayedItems.isEmpty) {
        lessonController.shuffleAllItems(
          isShuffle: true,
          shouldClear: false,
          from: 'app_data_controller',
        );
      }

      // Mark critical data as ready
      isCriticalDataReady.value = true;

      // Give a small delay for UI to settle
      await Future.delayed(const Duration(milliseconds: 100));

      // Mark all data as loaded
      isInitialDataLoaded.value = true;
    } catch (e) {
      print('Error preloading critical data: $e');
      // Still mark as loaded to prevent infinite loading
      isInitialDataLoaded.value = true;
      isCriticalDataReady.value = true;
    }
  }

  // Method to refresh data without causing UI jumps
  Future<void> refreshUserData() async {
    final profileController = Get.find<ProfileController>();
    await profileController.getUserData();

    // Update cached values smoothly
    cachedHearts.value =
        profileController.userr.value.hearts ?? cachedHearts.value;
    cachedNeurons.value =
        profileController.userr.value.neurons ?? cachedNeurons.value;
    cachedStreak.value =
        profileController.userr.value.weeklyStreak ?? cachedStreak.value;
    cachedIsPremium.value =
        profileController.userr.value.isPremiumUser ?? cachedIsPremium.value;
  }
}
